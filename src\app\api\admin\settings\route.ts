import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { UserRole } from '@/types/auth';

// GET - Get all settings (Admin only)
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: settings, error } = await supabaseAdmin
      .from('registration_settings')
      .select('*')
      .order('setting_key', { ascending: true });

    if (error) {
      console.error('Error fetching settings:', error);
      return NextResponse.json({ error: 'Failed to fetch settings' }, { status: 500 });
    }

    return NextResponse.json(settings);

  } catch (error) {
    console.error('Error in settings GET:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

// POST - Create new setting (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { setting_key, setting_value, description } = await request.json();

    if (!setting_key || !setting_value) {
      return NextResponse.json({ 
        error: 'Setting key and value are required' 
      }, { status: 400 });
    }

    // Check if setting already exists
    const { data: existingSetting } = await supabaseAdmin
      .from('registration_settings')
      .select('id')
      .eq('setting_key', setting_key)
      .single();

    if (existingSetting) {
      return NextResponse.json({ 
        error: 'Setting with this key already exists' 
      }, { status: 400 });
    }

    // Create setting
    const { data: newSetting, error } = await supabaseAdmin
      .from('registration_settings')
      .insert({
        setting_key,
        setting_value,
        description: description || null,
        updated_by: session.user.id
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating setting:', error);
      return NextResponse.json({ error: 'Failed to create setting' }, { status: 500 });
    }

    return NextResponse.json(newSetting);

  } catch (error) {
    console.error('Error in settings POST:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

// PATCH - Update setting (Admin only)
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { setting_key, setting_value, description } = await request.json();

    if (!setting_key || !setting_value) {
      return NextResponse.json({ 
        error: 'Setting key and value are required' 
      }, { status: 400 });
    }

    // Update setting
    const { data: updatedSetting, error } = await supabaseAdmin
      .from('registration_settings')
      .update({
        setting_value,
        description: description || null,
        updated_by: session.user.id
      })
      .eq('setting_key', setting_key)
      .select()
      .single();

    if (error) {
      console.error('Error updating setting:', error);
      return NextResponse.json({ error: 'Failed to update setting' }, { status: 500 });
    }

    return NextResponse.json(updatedSetting);

  } catch (error) {
    console.error('Error in settings PATCH:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

'use client';

import { useState } from 'react';
import Link from 'next/link';
import { validateEmail, validatePhone } from '@/lib/utils';
import Logo from '@/components/ui/logo';
import { showToast } from '@/lib/toast';

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    pincode: '',
    password: '',
    confirmPassword: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.phone) {
      newErrors.phone = 'Phone number is required for retailers';
    } else if (!validatePhone(formData.phone)) {
      newErrors.phone = 'Please enter a valid 10-digit phone number';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Shop/Business address is required';
    }

    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }

    if (!formData.state.trim()) {
      newErrors.state = 'State is required';
    }

    if (!formData.pincode.trim()) {
      newErrors.pincode = 'PIN code is required';
    } else if (!/^\d{6}$/.test(formData.pincode)) {
      newErrors.pincode = 'Please enter a valid 6-digit PIN code';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters long';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          address: formData.address,
          city: formData.city,
          state: formData.state,
          pincode: formData.pincode,
          password: formData.password,
          role: 'RETAILER',
        }),
      });

      const data = await response.json();

      if (response.ok) {
        showToast.success('Registration request submitted!', {
          description: 'Your registration is pending admin approval. You will be notified once approved.'
        });
        setTimeout(() => {
          window.location.href = '/';
        }, 3000);
      } else {
        showToast.error('Registration failed', {
          description: data.error || 'Please try again.'
        });
      }
    } catch (error) {
      showToast.error('Registration error', {
        description: 'An error occurred during registration. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-red-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative">
      {/* Akrix.ai Branding - Top Right Corner */}
      <div className="absolute top-4 right-4 z-10">
        <a
          href="https://akrix-ai.netlify.app"
          target="_blank"
          rel="noopener noreferrer"
          className="text-red-600/80 hover:text-red-600 text-xs font-medium px-3 py-2 rounded-lg hover:bg-white/50 transition-all duration-200 flex items-center space-x-1 backdrop-blur-sm"
        >
          <span className="text-xs">🚀</span>
          <span>Developed by Akrix.ai</span>
        </a>
      </div>

      <div className="max-w-2xl w-full space-y-8">
        {/* Header */}
        <div className="text-center animate-fade-in">
          <Link href="/" className="inline-block mb-6">
            <Logo size="lg" showText={true} animated={true} />
          </Link>
          <h2 className="text-4xl font-extrabold text-red-800 mb-4">
            Become a Retailer Partner
          </h2>
          <div className="bg-gradient-to-r from-red-600 to-red-700 text-white p-4 rounded-xl shadow-lg">
            <div className="flex items-center justify-center space-x-3 mb-2">
              <span className="text-3xl">🏪</span>
              <span className="text-xl font-bold">Retailer Registration</span>
            </div>
            <p className="text-red-100 text-sm">
              Join our network and start earning by providing government services
            </p>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="bg-white p-6 rounded-xl shadow-lg border border-red-200 animate-scale-in">
          <h3 className="text-lg font-bold text-red-800 mb-4 text-center">🎯 Retailer Benefits</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl mb-2">💰</div>
              <p className="text-sm font-medium text-red-700">Earn Commission</p>
              <p className="text-xs text-red-500">On every service</p>
            </div>
            <div>
              <div className="text-2xl mb-2">🏪</div>
              <p className="text-sm font-medium text-red-700">Own Business</p>
              <p className="text-xs text-red-500">Be your own boss</p>
            </div>
            <div>
              <div className="text-2xl mb-2">📈</div>
              <p className="text-sm font-medium text-red-700">Grow Income</p>
              <p className="text-xs text-red-500">Unlimited potential</p>
            </div>
          </div>
        </div>

        {/* Payment QR and Amount Section */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl shadow-md p-6 flex flex-col items-center my-4 animate-fade-in">
          <h3 className="text-xl font-bold text-yellow-800 mb-2">Pay Registration Fee</h3>
          <p className="text-yellow-700 mb-4 text-center">To register as a retailer, please pay <span className="font-bold text-lg text-yellow-900">₹499</span> using the QR code below. Complete the payment before submitting your registration.</p>
          <img src="/akrixPayQR.jpg" alt="Akrix Pay QR" className="w-96 h-96 object-contain rounded-lg border-2 border-yellow-300 shadow" />
          <p className="text-xs text-yellow-600 mt-2 text-center">Scan the QR code above with any UPI app to pay ₹499 for retailer registration and contact the Admin <span className="font-bold text-xs text-yellow-900">phone number - 7499116527</span> or <span className="font-bold text-xs text-yellow-900">email - <EMAIL></span> to get the approval.</p>
        </div>

        {/* Registration Form */}
        <form className="bg-white p-8 rounded-xl shadow-xl border border-red-200 animate-slide-in-left" onSubmit={handleSubmit}>
          <h3 className="text-2xl font-bold text-red-800 mb-6 text-center">📝 Registration Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Name */}
            <div className="md:col-span-2">
              <label htmlFor="name" className="block text-sm font-medium text-red-700 mb-2">
                Full Name / Business Name *
              </label>
              <input
                id="name"
                name="name"
                type="text"
                required
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 bg-white transition-colors ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter your full name or business name"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-red-700 mb-2">
                Email Address *
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 bg-white transition-colors ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter your email address"
              />
              {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
            </div>

            {/* Phone */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-red-700 mb-2">
                Phone Number *
              </label>
              <input
                id="phone"
                name="phone"
                type="tel"
                required
                value={formData.phone}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 bg-white transition-colors ${
                  errors.phone ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter your 10-digit phone number"
              />
              {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone}</p>}
            </div>

            {/* Address */}
            <div className="md:col-span-2">
              <label htmlFor="address" className="block text-sm font-medium text-red-700 mb-2">
                Shop/Business Address *
              </label>
              <textarea
                id="address"
                name="address"
                required
                value={formData.address}
                onChange={handleInputChange}
                rows={3}
                className={`w-full px-4 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 bg-white transition-colors ${
                  errors.address ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter your complete shop/business address"
              />
              {errors.address && <p className="mt-1 text-sm text-red-600">{errors.address}</p>}
            </div>

            {/* City */}
            <div>
              <label htmlFor="city" className="block text-sm font-medium text-red-700 mb-2">
                City *
              </label>
              <input
                id="city"
                name="city"
                type="text"
                required
                value={formData.city}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 bg-white transition-colors ${
                  errors.city ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter your city"
              />
              {errors.city && <p className="mt-1 text-sm text-red-600">{errors.city}</p>}
            </div>

            {/* State */}
            <div>
              <label htmlFor="state" className="block text-sm font-medium text-red-700 mb-2">
                State *
              </label>
              <input
                id="state"
                name="state"
                type="text"
                required
                value={formData.state}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 bg-white transition-colors ${
                  errors.state ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter your state"
              />
              {errors.state && <p className="mt-1 text-sm text-red-600">{errors.state}</p>}
            </div>

            {/* PIN Code */}
            <div>
              <label htmlFor="pincode" className="block text-sm font-medium text-red-700 mb-2">
                PIN Code *
              </label>
              <input
                id="pincode"
                name="pincode"
                type="text"
                required
                value={formData.pincode}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 bg-white transition-colors ${
                  errors.pincode ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter 6-digit PIN code"
                maxLength={6}
              />
              {errors.pincode && <p className="mt-1 text-sm text-red-600">{errors.pincode}</p>}
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-red-700 mb-2">
                Password *
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 bg-white transition-colors ${
                  errors.password ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Create a strong password (min. 8 characters)"
              />
              {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
            </div>

            {/* Confirm Password */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-red-700 mb-2">
                Confirm Password *
              </label>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                required
                value={formData.confirmPassword}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 bg-white transition-colors ${
                  errors.confirmPassword ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Confirm your password"
              />
              {errors.confirmPassword && <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>}
            </div>
          </div>

          {/* Terms and Submit */}
          <div className="md:col-span-2 space-y-6 mt-8">
            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <h4 className="font-semibold text-red-800 mb-2">📋 Terms & Conditions</h4>
              <ul className="text-sm text-red-700 space-y-1">
                <li>• You must have a valid business license or shop</li>
                <li>• Minimum age requirement: 18 years</li>
                <li>• You agree to provide accurate customer service</li>
                <li>• Commission rates as per government guidelines</li>
              </ul>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className={`w-full flex justify-center items-center py-4 px-6 border border-transparent text-lg font-bold rounded-xl text-white transition-all duration-200 ${
                isLoading
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-4 focus:ring-red-300 shadow-lg hover:shadow-xl animate-glow'
              }`}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating Retailer Account...
                </>
              ) : (
                <>
                  🏪 Register as Retailer Partner
                </>
              )}
            </button>

            {/* Links */}
            <div className="text-center space-y-2">
              <div className="text-sm text-gray-600">
                Already have a retailer account?{' '}
                <Link href="/login?role=retailer" className="font-medium text-red-600 hover:text-red-700 transition-colors">
                  Sign in here
                </Link>
              </div>
              <div className="text-xs text-gray-500">
                Need help? Contact support at{' '}
                <a href="mailto:<EMAIL>" className="text-red-600 hover:text-red-700">
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </form>

        {/* Back to Home */}
        <div className="text-center animate-fade-in">
          <Link href="/" className="inline-flex items-center text-red-600 hover:text-red-700 transition-colors font-medium">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
}

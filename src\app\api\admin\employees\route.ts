import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { supabaseAdmin } from '@/lib/supabase';
import { UserRole } from '@/types';
import bcrypt from 'bcryptjs';

// GET - Get all employees (Admin only)
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: employees, error } = await supabaseAdmin
      .from('users')
      .select(`
        *,
        employee_documents (
          id,
          aadhar_url,
          pan_url,
          photo_url,
          created_at,
          updated_at
        )
      `)
      .eq('role', 'EMPLOYEE')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching employees:', error);
      return NextResponse.json({ error: 'Failed to fetch employees' }, { status: 500 });
    }

    return NextResponse.json(employees);

  } catch (error) {
    console.error('Error in employees GET:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Create new employee (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const phone = formData.get('phone') as string;
    const password = formData.get('password') as string;
    const employee_id = formData.get('employee_id') as string;
    const department = formData.get('department') as string;

    // Get document files
    const aadharFile = formData.get('aadhar') as File | null;
    const panFile = formData.get('pan') as File | null;
    const photoFile = formData.get('photo') as File | null;

    if (!name || !email || !password) {
      return NextResponse.json({
        error: 'Name, email, and password are required'
      }, { status: 400 });
    }

    // Check if email already exists
    const { data: existingUser } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      return NextResponse.json({
        error: 'Email already exists'
      }, { status: 400 });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create employee
    const { data: newEmployee, error } = await supabaseAdmin
      .from('users')
      .insert({
        name,
        email,
        phone: phone || null,
        password_hash: passwordHash,
        role: 'EMPLOYEE',
        employee_id: employee_id || null,
        department: department || null,
        is_active: true,
        created_by: session.user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating employee:', error);
      return NextResponse.json({ error: 'Failed to create employee' }, { status: 500 });
    }

    // Upload documents if provided
    let aadharUrl = null;
    let panUrl = null;
    let photoUrl = null;

    if (aadharFile) {
      const aadharFileName = `${newEmployee.id}/aadhar_${Date.now()}.${aadharFile.name.split('.').pop()}`;
      const { data: aadharUpload, error: aadharError } = await supabaseAdmin.storage
        .from('employee-documents')
        .upload(aadharFileName, aadharFile);

      if (aadharError) {
        console.error('Error uploading Aadhar:', aadharError);
      } else {
        aadharUrl = aadharUpload.path;
      }
    }

    if (panFile) {
      const panFileName = `${newEmployee.id}/pan_${Date.now()}.${panFile.name.split('.').pop()}`;
      const { data: panUpload, error: panError } = await supabaseAdmin.storage
        .from('employee-documents')
        .upload(panFileName, panFile);

      if (panError) {
        console.error('Error uploading PAN:', panError);
      } else {
        panUrl = panUpload.path;
      }
    }

    if (photoFile) {
      const photoFileName = `${newEmployee.id}/photo_${Date.now()}.${photoFile.name.split('.').pop()}`;
      const { data: photoUpload, error: photoError } = await supabaseAdmin.storage
        .from('employee-documents')
        .upload(photoFileName, photoFile);

      if (photoError) {
        console.error('Error uploading Photo:', photoError);
      } else {
        photoUrl = photoUpload.path;
      }
    }

    // Create employee documents record if any documents were uploaded
    if (aadharUrl || panUrl || photoUrl) {
      const { error: docError } = await supabaseAdmin
        .from('employee_documents')
        .insert({
          user_id: newEmployee.id,
          aadhar_url: aadharUrl,
          pan_url: panUrl,
          photo_url: photoUrl,
          created_by: session.user.id
        });

      if (docError) {
        console.error('Error creating employee documents record:', docError);
      }
    }

    return NextResponse.json(newEmployee);

  } catch (error) {
    console.error('Error in employees POST:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}

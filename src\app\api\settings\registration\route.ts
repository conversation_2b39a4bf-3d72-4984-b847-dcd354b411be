import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-admin';

// GET - Get registration settings (Public endpoint)
export async function GET() {
  try {
    const { data: settings, error } = await supabaseAdmin
      .from('registration_settings')
      .select('setting_key, setting_value')
      .eq('setting_key', 'registration_fee')
      .single();

    if (error) {
      console.error('Error fetching registration settings:', error);
      return NextResponse.json({ error: 'Failed to fetch registration settings' }, { status: 500 });
    }

    return NextResponse.json({
      registration_fee: settings?.setting_value || '499'
    });

  } catch (error) {
    console.error('Error in registration settings GET:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import DashboardLayout from '@/components/dashboard/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UserRole } from '@/types';
import { Settings, Save, DollarSign } from 'lucide-react';
import { showToast } from '@/lib/toast';

interface Setting {
  id: string;
  setting_key: string;
  setting_value: string;
  description: string | null;
  created_at: string;
  updated_at: string;
}

export default function AdminSettingsPage() {
  const { data: session } = useSession();
  const [settings, setSettings] = useState<Setting[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [registrationFee, setRegistrationFee] = useState('');

  useEffect(() => {
    if (session?.user?.role === UserRole.ADMIN) {
      fetchSettings();
    }
  }, [session]);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings');
      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }
      const data = await response.json();
      setSettings(data);
      
      // Find registration fee setting
      const regFeeSetting = data.find((s: Setting) => s.setting_key === 'registration_fee');
      if (regFeeSetting) {
        setRegistrationFee(regFeeSetting.setting_value);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      showToast.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveRegistrationFee = async () => {
    if (!registrationFee || isNaN(Number(registrationFee))) {
      showToast.error('Please enter a valid registration fee amount');
      return;
    }

    setSaving(true);
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          setting_key: 'registration_fee',
          setting_value: registrationFee,
          description: 'Registration fee amount in INR'
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update registration fee');
      }

      showToast.success('Registration fee updated successfully');
      fetchSettings(); // Refresh settings
    } catch (error) {
      console.error('Error updating registration fee:', error);
      showToast.error('Failed to update registration fee');
    } finally {
      setSaving(false);
    }
  };

  if (!session || session.user.role !== UserRole.ADMIN) {
    return (
      <DashboardLayout>
        <div className="text-center py-8">
          <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
          <p className="text-gray-600 mt-2">You don't have permission to access this page.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
            <p className="text-gray-600">Manage application settings and configurations</p>
          </div>
        </div>

        {/* Registration Fee Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="w-5 h-5 mr-2" />
              Registration Fee Settings
            </CardTitle>
            <CardDescription>
              Configure the registration fee displayed on the registration page
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto"></div>
                <p className="text-gray-600 mt-2">Loading settings...</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Registration Fee Amount (₹)
                  </label>
                  <div className="flex items-center space-x-3">
                    <div className="relative flex-1 max-w-xs">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">₹</span>
                      <input
                        type="number"
                        value={registrationFee}
                        onChange={(e) => setRegistrationFee(e.target.value)}
                        className="pl-8 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                        placeholder="Enter amount"
                        min="1"
                      />
                    </div>
                    <Button
                      onClick={handleSaveRegistrationFee}
                      disabled={saving}
                      className="bg-red-600 hover:bg-red-700 text-white"
                    >
                      {saving ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Saving...
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <Save className="w-4 h-4 mr-2" />
                          Save
                        </div>
                      )}
                    </Button>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    This amount will be displayed on the registration page for new user registrations.
                  </p>
                </div>

                {/* Current Settings Display */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Current Settings</h4>
                  <div className="space-y-2">
                    {settings.map((setting) => (
                      <div key={setting.id} className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">{setting.setting_key}:</span>
                        <span className="font-medium text-gray-900">
                          {setting.setting_key === 'registration_fee' ? `₹${setting.setting_value}` : setting.setting_value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Additional Settings Card - Placeholder for future settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Additional Settings
            </CardTitle>
            <CardDescription>
              More configuration options will be available here in the future
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-gray-500">
              <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p>Additional settings will be added here as needed</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}

{"name": "v<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "version": "1.0.0", "private": true, "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Government Services Portal", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next out"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.14", "@supabase/supabase-js": "^2.50.3", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "framer-motion": "^12.23.6", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "next": "15.3.5", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "razorpay": "^2.9.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.1", "xlsx": "^0.18.5", "zod": "^3.25.74"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}, "engines": {"node": ">=18.19.0", "npm": ">=10.2.3"}}
{"extends": ["next/core-web-vitals"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "prefer-const": "off", "@typescript-eslint/no-require-imports": "off", "react-hooks/rules-of-hooks": "off", "react-hooks/exhaustive-deps": "off", "react/no-unescaped-entities": "off", "@next/next/no-img-element": "off", "jsx-a11y/alt-text": "off", "@typescript-eslint/no-empty-object-type": "off"}}